# Server Configuration
PORT=8000
NODE_ENV=development

# JWT Configuration
# 生成方法: node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
# 或者: openssl rand -hex 64
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long-replace-this-with-generated-key
JWT_EXPIRES_IN=24h

# Cloudflare R2 Configuration
R2_ACCOUNT_ID=your-cloudflare-account-id
R2_ACCESS_KEY_ID=your-r2-access-key-id
R2_SECRET_ACCESS_KEY=your-r2-secret-access-key
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_REGION=auto

# R2 Public URL (可选，如果特定存储桶配置了自定义域名)
# 注意：这是针对单个存储桶的自定义域名，不是整个 R2 服务
# 例如: https://bucket-custom-domain.yourdomain.com
R2_PUBLIC_URL=

# Share Service Configuration
SHARE_DEFAULT_EXPIRES=24h

# Security Configuration
BCRYPT_ROUNDS=12
PRESIGNED_URL_EXPIRES=300

# Admin User Configuration
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password-here
