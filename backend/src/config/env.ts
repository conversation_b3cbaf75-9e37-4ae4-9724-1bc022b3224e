import { z } from 'zod'

const envSchema = z.object({
  // Server
  PORT: z.string().default('8000'),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  
  // JWT
  JWT_SECRET: z.string().min(32, 'JWT_SECRET must be at least 32 characters'),
  JWT_EXPIRES_IN: z.string().default('24h'),
  
  // Cloudflare R2
  R2_ACCOUNT_ID: z.string().min(1, 'R2_ACCOUNT_ID is required'),
  R2_ACCESS_KEY_ID: z.string().min(1, 'R2_ACCESS_KEY_ID is required'),
  R2_SECRET_ACCESS_KEY: z.string().min(1, 'R2_SECRET_ACCESS_KEY is required'),
  R2_ENDPOINT: z.string().url('R2_ENDPOINT must be a valid URL'),
  R2_REGION: z.string().default('auto'),

  // R2 Public URL (for buckets with custom domains)
  R2_PUBLIC_URL: z.string().url().optional(),

  // R2 Bucket Custom Domains (optional)
  // Format: bucket1=https://domain1.com,bucket2=https://domain2.com
  R2_BUCKET_DOMAINS: z.string().optional(),

  // Share service
  SHARE_DEFAULT_EXPIRES: z.string().default('24h'),
  
  // Security
  BCRYPT_ROUNDS: z.string().transform(Number).default('12'),
  PRESIGNED_URL_EXPIRES: z.string().transform(Number).default('300'), // 5 minutes

  // Admin User (configured via environment variables)
  ADMIN_USERNAME: z.string().min(1, 'ADMIN_USERNAME is required'),
  ADMIN_PASSWORD: z.string().min(6, 'ADMIN_PASSWORD must be at least 6 characters'),
})

function validateEnv() {
  try {
    return envSchema.parse(process.env)
  } catch (error) {
    console.error('❌ Environment validation failed:')
    if (error instanceof z.ZodError) {
      error.errors.forEach((err) => {
        console.error(`  ${err.path.join('.')}: ${err.message}`)
      })
    }
    process.exit(1)
  }
}

export const env = validateEnv()
export type Env = z.infer<typeof envSchema>
