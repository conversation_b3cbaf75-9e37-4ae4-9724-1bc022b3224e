import { Hono } from 'hono'
import { nanoid } from 'nanoid'
import { z } from 'zod'
import { generateFileUrl } from '../services/urlService'
import { env } from '../config/env'

const shareRoutes = new Hono()

// In-memory store for share links (use database in production)
interface ShareLink {
  id: string
  bucket: string
  key: string
  expiresAt: Date
  createdAt: Date
  accessCount: number
  maxAccess?: number
  password?: string
}

const shareLinks = new Map<string, ShareLink>()

const createShareSchema = z.object({
  bucket: z.string().min(1),
  key: z.string().min(1),
  expiresIn: z.string().default('24h'),
  maxAccess: z.number().optional(),
  password: z.string().optional(),
})

// Create share link
shareRoutes.post('/create', async (c) => {
  try {
    const body = await c.req.json()
    const { bucket, key, expiresIn, maxAccess, password } = createShareSchema.parse(body)

    // Parse expiration time
    const expirationMs = parseTimeString(expiresIn)
    const expiresAt = new Date(Date.now() + expirationMs)

    // Generate unique share ID
    const shareId = nanoid(10)

    // Store share link
    const shareLink: ShareLink = {
      id: shareId,
      bucket,
      key,
      expiresAt,
      createdAt: new Date(),
      accessCount: 0,
      maxAccess,
      password,
    }

    shareLinks.set(shareId, shareLink)

    // 直接返回分享ID，前端可以构建完整的分享URL
    const shareUrl = `${c.req.url.split('/api')[0]}/s/${shareId}`

    return c.json({
      shareId,
      shareUrl,
      expiresAt,
      maxAccess,
    })
  } catch (error) {
    console.error('Error creating share link:', error)
    return c.json({ error: 'Failed to create share link' }, 500)
  }
})

// Access shared file
shareRoutes.get('/:shareId', async (c) => {
  try {
    const shareId = c.req.param('shareId')
    const password = c.req.query('password')

    const shareLink = shareLinks.get(shareId)
    if (!shareLink) {
      return c.json({ error: 'Share link not found' }, 404)
    }

    // Check expiration
    if (shareLink.expiresAt < new Date()) {
      shareLinks.delete(shareId)
      return c.json({ error: 'Share link has expired' }, 410)
    }

    // Check access limit
    if (shareLink.maxAccess && shareLink.accessCount >= shareLink.maxAccess) {
      return c.json({ error: 'Share link access limit exceeded' }, 429)
    }

    // Check password
    if (shareLink.password && shareLink.password !== password) {
      return c.json({ error: 'Password required or incorrect' }, 401)
    }

    // 生成文件访问 URL（自动判断是否使用自定义域名）
    const urlResult = await generateFileUrl({
      bucket: shareLink.bucket,
      key: shareLink.key,
      expiresIn: 300, // 5 minutes for share links
    })

    // Increment access count
    shareLink.accessCount++

    return c.json({
      downloadUrl: urlResult.url,
      filename: shareLink.key.split('/').pop(),
      isPublic: urlResult.isPublic,
      expiresIn: urlResult.expiresIn,
    })
  } catch (error) {
    console.error('Error accessing shared file:', error)
    return c.json({ error: 'Failed to access shared file' }, 500)
  }
})

// Get share link info
shareRoutes.get('/:shareId/info', async (c) => {
  try {
    const shareId = c.req.param('shareId')
    const shareLink = shareLinks.get(shareId)

    if (!shareLink) {
      return c.json({ error: 'Share link not found' }, 404)
    }

    return c.json({
      id: shareLink.id,
      filename: shareLink.key.split('/').pop(),
      expiresAt: shareLink.expiresAt,
      accessCount: shareLink.accessCount,
      maxAccess: shareLink.maxAccess,
      hasPassword: !!shareLink.password,
      isExpired: shareLink.expiresAt < new Date(),
    })
  } catch (error) {
    console.error('Error getting share info:', error)
    return c.json({ error: 'Failed to get share info' }, 500)
  }
})

// List user's share links
shareRoutes.get('/', async (c) => {
  try {
    const userShares = Array.from(shareLinks.values())
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .map(share => ({
        id: share.id,
        filename: share.key.split('/').pop(),
        bucket: share.bucket,
        key: share.key,
        createdAt: share.createdAt,
        expiresAt: share.expiresAt,
        accessCount: share.accessCount,
        maxAccess: share.maxAccess,
        hasPassword: !!share.password,
        isExpired: share.expiresAt < new Date(),
      }))

    return c.json({ shares: userShares })
  } catch (error) {
    console.error('Error listing shares:', error)
    return c.json({ error: 'Failed to list shares' }, 500)
  }
})

// Delete share link
shareRoutes.delete('/:shareId', async (c) => {
  try {
    const shareId = c.req.param('shareId')
    
    if (!shareLinks.has(shareId)) {
      return c.json({ error: 'Share link not found' }, 404)
    }

    shareLinks.delete(shareId)
    return c.json({ message: 'Share link deleted successfully' })
  } catch (error) {
    console.error('Error deleting share link:', error)
    return c.json({ error: 'Failed to delete share link' }, 500)
  }
})

function parseTimeString(timeStr: string): number {
  const units: Record<string, number> = {
    s: 1000,
    m: 60 * 1000,
    h: 60 * 60 * 1000,
    d: 24 * 60 * 60 * 1000,
  }

  const match = timeStr.match(/^(\d+)([smhd])$/)
  if (!match) {
    throw new Error('Invalid time format. Use format like "24h", "30m", "7d"')
  }

  const [, value, unit] = match
  return parseInt(value) * units[unit]
}

export { shareRoutes }
