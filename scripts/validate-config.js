#!/usr/bin/env node

/**
 * 配置验证脚本
 * 检查环境变量配置是否正确
 */

const fs = require('fs')
const path = require('path')

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
}

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function loadEnvFile() {
  const envPath = path.join(process.cwd(), 'backend', '.env')
  
  if (!fs.existsSync(envPath)) {
    colorLog('red', '❌ 环境配置文件不存在: backend/.env')
    colorLog('yellow', '💡 请先运行配置脚本: npm run configure')
    process.exit(1)
  }

  const envContent = fs.readFileSync(envPath, 'utf8')
  const env = {}
  
  envContent.split('\n').forEach(line => {
    const trimmed = line.trim()
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=')
      if (key && valueParts.length > 0) {
        env[key] = valueParts.join('=')
      }
    }
  })

  return env
}

function validateConfig(env) {
  const errors = []
  const warnings = []
  const info = []

  // 必需配置检查
  const required = [
    'JWT_SECRET',
    'R2_ACCOUNT_ID',
    'R2_ACCESS_KEY_ID',
    'R2_SECRET_ACCESS_KEY',
    'R2_ENDPOINT'
  ]

  required.forEach(key => {
    if (!env[key] || env[key].trim() === '') {
      errors.push(`${key} 未配置或为空`)
    }
  })

  // JWT 密钥强度检查
  if (env.JWT_SECRET) {
    if (env.JWT_SECRET.length < 32) {
      errors.push('JWT_SECRET 长度不足 32 字符，安全性不够')
    } else if (env.JWT_SECRET.length < 64) {
      warnings.push('JWT_SECRET 建议使用 64 字符以上的密钥')
    }
    
    if (env.JWT_SECRET.includes('your-') || env.JWT_SECRET.includes('replace-this')) {
      errors.push('JWT_SECRET 仍使用默认值，请生成新的密钥')
    }
  }

  // R2 配置检查
  if (env.R2_ACCOUNT_ID && (env.R2_ACCOUNT_ID.includes('your-') || env.R2_ACCOUNT_ID.length < 10)) {
    errors.push('R2_ACCOUNT_ID 格式不正确或仍使用默认值')
  }

  if (env.R2_ACCESS_KEY_ID && (env.R2_ACCESS_KEY_ID.includes('your-') || env.R2_ACCESS_KEY_ID.length < 10)) {
    errors.push('R2_ACCESS_KEY_ID 格式不正确或仍使用默认值')
  }

  if (env.R2_SECRET_ACCESS_KEY && (env.R2_SECRET_ACCESS_KEY.includes('your-') || env.R2_SECRET_ACCESS_KEY.length < 20)) {
    errors.push('R2_SECRET_ACCESS_KEY 格式不正确或仍使用默认值')
  }

  // R2 端点检查
  if (env.R2_ENDPOINT) {
    if (!env.R2_ENDPOINT.startsWith('https://')) {
      errors.push('R2_ENDPOINT 必须使用 HTTPS')
    }
    
    if (!env.R2_ENDPOINT.includes('.r2.cloudflarestorage.com')) {
      warnings.push('R2_ENDPOINT 格式可能不正确，应为: https://account-id.r2.cloudflarestorage.com')
    }
  }

  // 自定义域名检查
  if (env.R2_PUBLIC_URL) {
    if (!env.R2_PUBLIC_URL.startsWith('https://')) {
      warnings.push('R2_PUBLIC_URL 建议使用 HTTPS')
    }
    info.push('已配置自定义域名，文件将通过您的域名直接访问')
  } else {
    info.push('未配置自定义域名，将使用预签名 URL 访问文件')
  }

  // 端口检查
  if (env.PORT) {
    const port = parseInt(env.PORT)
    if (isNaN(port) || port < 1 || port > 65535) {
      errors.push('PORT 必须是 1-65535 之间的数字')
    }
  }

  // 过期时间格式检查
  const timeFields = ['JWT_EXPIRES_IN', 'SHARE_DEFAULT_EXPIRES']
  timeFields.forEach(field => {
    if (env[field] && !env[field].match(/^\d+[smhd]$/)) {
      warnings.push(`${field} 格式可能不正确，应为如: 24h, 30m, 7d`)
    }
  })

  return { errors, warnings, info }
}

async function testR2Connection(env) {
  colorLog('blue', '🔍 测试 R2 连接...')
  
  try {
    // 这里可以添加实际的 R2 连接测试
    // 由于需要 AWS SDK，这里只做基本格式验证
    
    if (!env.R2_ACCOUNT_ID || !env.R2_ACCESS_KEY_ID || !env.R2_SECRET_ACCESS_KEY) {
      colorLog('yellow', '⚠️  无法测试 R2 连接：缺少必要的认证信息')
      return false
    }

    colorLog('green', '✅ R2 配置格式正确')
    colorLog('yellow', '💡 实际连接测试需要启动应用后进行')
    return true
  } catch (error) {
    colorLog('red', `❌ R2 连接测试失败: ${error.message}`)
    return false
  }
}

function generateReport(validation, env) {
  console.log()
  colorLog('cyan', '📋 配置验证报告')
  console.log('='.repeat(50))

  // 错误
  if (validation.errors.length > 0) {
    console.log()
    colorLog('red', '❌ 错误 (必须修复):')
    validation.errors.forEach(error => {
      console.log(`   • ${error}`)
    })
  }

  // 警告
  if (validation.warnings.length > 0) {
    console.log()
    colorLog('yellow', '⚠️  警告 (建议修复):')
    validation.warnings.forEach(warning => {
      console.log(`   • ${warning}`)
    })
  }

  // 信息
  if (validation.info.length > 0) {
    console.log()
    colorLog('blue', 'ℹ️  信息:')
    validation.info.forEach(info => {
      console.log(`   • ${info}`)
    })
  }

  // 配置摘要
  console.log()
  colorLog('cyan', '📊 配置摘要:')
  console.log(`   • 服务器端口: ${env.PORT || '8000'}`)
  console.log(`   • 运行环境: ${env.NODE_ENV || 'development'}`)
  console.log(`   • JWT 过期时间: ${env.JWT_EXPIRES_IN || '24h'}`)
  console.log(`   • 分享默认过期: ${env.SHARE_DEFAULT_EXPIRES || '24h'}`)
  console.log(`   • 自定义域名: ${env.R2_PUBLIC_URL ? '已配置' : '未配置'}`)

  // 总结
  console.log()
  if (validation.errors.length === 0) {
    colorLog('green', '🎉 配置验证通过！')
    console.log()
    colorLog('cyan', '下一步操作:')
    console.log('1. 运行 "npm run dev" 启动开发服务器')
    console.log('2. 访问 http://localhost:' + (env.PORT || '8000'))
    console.log('3. 测试文件上传和管理功能')
  } else {
    colorLog('red', '❌ 配置验证失败，请修复上述错误后重试')
    console.log()
    colorLog('cyan', '修复建议:')
    console.log('1. 运行 "npm run configure" 重新配置')
    console.log('2. 手动编辑 backend/.env 文件')
    console.log('3. 参考文档: docs/R2_CONFIGURATION.md')
  }
}

async function main() {
  console.clear()
  colorLog('cyan', '🔍 CF-R2 Manager 配置验证')
  console.log('='.repeat(40))

  try {
    const env = loadEnvFile()
    const validation = validateConfig(env)
    
    await testR2Connection(env)
    generateReport(validation, env)
    
    process.exit(validation.errors.length > 0 ? 1 : 0)
  } catch (error) {
    colorLog('red', `❌ 验证过程中发生错误: ${error.message}`)
    process.exit(1)
  }
}

// 运行主函数
if (require.main === module) {
  main()
}

module.exports = { validateConfig, loadEnvFile }
