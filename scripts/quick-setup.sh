#!/bin/bash

# CF-R2 Manager 快速配置脚本
# 用于快速设置基本环境变量

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_header() {
    echo
    print_color $CYAN "🚀 CF-R2 Manager 快速配置"
    echo "=================================="
    echo
}

print_step() {
    local step=$1
    local title=$2
    echo
    print_color $BLUE "📋 ${step}. ${title}"
    echo "$(printf '─%.0s' {1..30})"
}

# 检查必要工具
check_requirements() {
    if ! command -v node &> /dev/null; then
        print_color $RED "❌ Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    if ! command -v bun &> /dev/null; then
        print_color $YELLOW "⚠️  Bun 未安装，建议安装 Bun 以获得更好的性能"
        echo "   安装命令: curl -fsSL https://bun.sh/install | bash"
    fi
}

# 主函数
main() {
    print_header
    
    print_color $YELLOW "本脚本提供两种配置方式："
    echo "1. 交互式配置向导 (推荐)"
    echo "2. 手动配置模板"
    echo
    
    read -p "请选择配置方式 (1/2): " choice
    
    case $choice in
        1)
            interactive_setup
            ;;
        2)
            template_setup
            ;;
        *)
            print_color $RED "❌ 无效选择"
            exit 1
            ;;
    esac
}

# 交互式配置
interactive_setup() {
    print_step "1" "启动交互式配置向导"
    
    if [ ! -f "scripts/configure-env.js" ]; then
        print_color $RED "❌ 配置脚本不存在"
        exit 1
    fi
    
    print_color $GREEN "✅ 启动交互式配置向导..."
    node scripts/configure-env.js
}

# 模板配置
template_setup() {
    print_step "1" "创建配置模板"
    
    local env_file="backend/.env"
    local env_example="backend/.env.example"
    
    if [ ! -f "$env_example" ]; then
        print_color $RED "❌ 环境变量模板文件不存在: $env_example"
        exit 1
    fi
    
    # 备份现有文件
    if [ -f "$env_file" ]; then
        local backup_file="${env_file}.backup.$(date +%s)"
        cp "$env_file" "$backup_file"
        print_color $YELLOW "📋 已备份现有配置到: $backup_file"
    fi
    
    # 复制模板
    cp "$env_example" "$env_file"
    
    # 生成 JWT 密钥
    print_step "2" "生成 JWT 密钥"
    local jwt_secret=$(node -e "console.log(require('crypto').randomBytes(64).toString('hex'))")
    
    # 替换 JWT 密钥
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/JWT_SECRET=.*/JWT_SECRET=$jwt_secret/" "$env_file"
    else
        # Linux
        sed -i "s/JWT_SECRET=.*/JWT_SECRET=$jwt_secret/" "$env_file"
    fi
    
    print_color $GREEN "✅ 配置模板已创建: $env_file"
    print_color $GREEN "✅ JWT 密钥已自动生成"
    
    print_step "3" "配置说明"
    
    print_color $YELLOW "请编辑 $env_file 文件，填入以下必需信息："
    echo
    echo "🔑 Cloudflare R2 配置 (必需):"
    echo "   R2_ACCOUNT_ID=your-cloudflare-account-id"
    echo "   R2_ACCESS_KEY_ID=your-r2-access-key-id"
    echo "   R2_SECRET_ACCESS_KEY=your-r2-secret-access-key"
    echo
    echo "🌐 自定义域名配置 (可选):"
    echo "   R2_PUBLIC_URL=https://files.yourdomain.com"
    echo
    
    print_color $CYAN "📚 获取 R2 配置信息："
    echo "1. 登录 Cloudflare Dashboard"
    echo "2. 进入 R2 Object Storage 页面"
    echo "3. 点击 'Manage R2 API tokens'"
    echo "4. 创建新的 API Token"
    echo "5. 复制 Account ID、Access Key ID 和 Secret Access Key"
    echo
    
    print_color $CYAN "🌐 配置自定义域名 (可选)："
    echo "1. 在 R2 存储桶设置中添加自定义域名"
    echo "2. 在 .env 文件中设置 R2_PUBLIC_URL"
    echo "3. 重启应用即可生效"
    echo
    
    read -p "是否现在打开配置文件进行编辑？(y/N): " edit_now
    if [[ $edit_now =~ ^[Yy]$ ]]; then
        if command -v code &> /dev/null; then
            code "$env_file"
        elif command -v nano &> /dev/null; then
            nano "$env_file"
        elif command -v vim &> /dev/null; then
            vim "$env_file"
        else
            print_color $YELLOW "⚠️  未找到合适的编辑器，请手动编辑: $env_file"
        fi
    fi
}

# 完成提示
show_completion() {
    echo
    print_color $GREEN "🎉 配置完成！"
    echo "=================================="
    echo
    print_color $CYAN "下一步操作："
    echo "1. 确保已正确配置 Cloudflare R2 信息"
    echo "2. 运行 'bun run dev' 或 'npm run dev' 启动开发服务器"
    echo "3. 访问 http://localhost:8000 查看应用"
    echo
    print_color $YELLOW "💡 提示："
    echo "- 配置文件位置: backend/.env"
    echo "- 如需重新配置，可重新运行此脚本"
    echo "- 详细文档请查看: docs/R2_CONFIGURATION.md"
    echo
}

# 错误处理
trap 'print_color $RED "❌ 配置过程中断"; exit 1' INT

# 检查环境
check_requirements

# 运行主函数
main

# 显示完成信息
show_completion
