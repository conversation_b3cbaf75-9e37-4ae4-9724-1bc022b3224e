#!/usr/bin/env node

/**
 * 交互式环境变量配置脚本
 * 引导用户正确配置 Cloudflare R2 和应用设置
 */

const readline = require('readline')
const fs = require('fs')
const path = require('path')
const crypto = require('crypto')

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve)
  })
}

async function main() {
  console.clear()
  colorLog('cyan', '🚀 Cloudflare R2 文件管理器 - 环境配置向导')
  console.log('=' * 50)
  console.log()

  colorLog('yellow', '本向导将帮助您配置以下内容：')
  console.log('1. JWT 密钥生成')
  console.log('2. Cloudflare R2 连接配置')
  console.log('3. 自定义域名设置（可选）')
  console.log('4. 其他安全设置')
  console.log()

  const proceed = await question('是否继续配置？(y/N): ')
  if (proceed.toLowerCase() !== 'y') {
    colorLog('yellow', '配置已取消')
    process.exit(0)
  }

  const config = {}

  // 1. 基础服务器配置
  console.log()
  colorLog('blue', '📋 1. 基础服务器配置')
  console.log('─'.repeat(30))

  config.PORT = await question('服务器端口 (默认: 8000): ') || '8000'
  config.NODE_ENV = await question('运行环境 (development/production, 默认: development): ') || 'development'

  // 2. JWT 配置
  console.log()
  colorLog('blue', '🔐 2. JWT 安全配置')
  console.log('─'.repeat(30))

  const generateJWT = await question('是否自动生成 JWT 密钥？(Y/n): ')
  if (generateJWT.toLowerCase() !== 'n') {
    config.JWT_SECRET = crypto.randomBytes(64).toString('hex')
    colorLog('green', '✅ JWT 密钥已自动生成')
  } else {
    config.JWT_SECRET = await question('请输入 JWT 密钥 (至少32字符): ')
    if (config.JWT_SECRET.length < 32) {
      colorLog('red', '❌ JWT 密钥长度不足，自动生成新密钥')
      config.JWT_SECRET = crypto.randomBytes(64).toString('hex')
    }
  }

  config.JWT_EXPIRES_IN = await question('JWT 过期时间 (默认: 24h): ') || '24h'

  // 3. Cloudflare R2 配置
  console.log()
  colorLog('blue', '☁️  3. Cloudflare R2 配置')
  console.log('─'.repeat(30))

  colorLog('yellow', '请在 Cloudflare Dashboard 中获取以下信息：')
  console.log('1. 进入 R2 Object Storage 页面')
  console.log('2. 点击 "Manage R2 API tokens"')
  console.log('3. 创建新的 API Token')
  console.log()

  config.R2_ACCOUNT_ID = await question('Cloudflare 账户 ID: ')
  if (!config.R2_ACCOUNT_ID) {
    colorLog('red', '❌ 账户 ID 不能为空')
    process.exit(1)
  }

  config.R2_ACCESS_KEY_ID = await question('R2 Access Key ID: ')
  if (!config.R2_ACCESS_KEY_ID) {
    colorLog('red', '❌ Access Key ID 不能为空')
    process.exit(1)
  }

  config.R2_SECRET_ACCESS_KEY = await question('R2 Secret Access Key: ')
  if (!config.R2_SECRET_ACCESS_KEY) {
    colorLog('red', '❌ Secret Access Key 不能为空')
    process.exit(1)
  }

  // 自动生成 R2 端点
  config.R2_ENDPOINT = `https://${config.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`
  config.R2_REGION = 'auto'

  colorLog('green', `✅ R2 端点已自动生成: ${config.R2_ENDPOINT}`)

  // 4. 自定义域名配置
  console.log()
  colorLog('blue', '🌐 4. 自定义域名配置 (可选)')
  console.log('─'.repeat(30))

  colorLog('yellow', '注意：R2 自定义域名是针对单个存储桶配置的')
  console.log('如果您在 Cloudflare 控制台为某个存储桶配置了自定义域名：')
  console.log('1. 进入特定存储桶的设置页面')
  console.log('2. 在"自定义域"部分添加域名（如: files.yourdomain.com）')
  console.log('3. 在这里填入完整的域名 URL')
  console.log()
  console.log('示例: https://files.yourdomain.com')
  console.log('如果没有配置或使用多个存储桶，直接按回车跳过')
  console.log()

  const customDomain = await question('存储桶自定义域名 URL (可选): ')
  if (customDomain) {
    if (customDomain.startsWith('http://') || customDomain.startsWith('https://')) {
      config.R2_PUBLIC_URL = customDomain
      colorLog('green', '✅ 存储桶自定义域名已配置')
      colorLog('yellow', '⚠️  此配置仅适用于配置了该域名的存储桶')
    } else {
      colorLog('yellow', '⚠️  域名格式不正确，已跳过自定义域名配置')
    }
  } else {
    colorLog('blue', 'ℹ️  未配置自定义域名，将使用预签名 URL 访问文件')
  }

  // 5. 管理员账户配置
  console.log()
  colorLog('blue', '👤 5. 管理员账户配置')
  console.log('─'.repeat(30))

  const adminUsername = await question('管理员用户名 (默认: admin): ') || 'admin'
  const adminPassword = await question('管理员密码 (至少6位): ')

  if (!adminPassword || adminPassword.length < 6) {
    colorLog('red', '❌ 密码至少需要6位字符')
    process.exit(1)
  }

  config.ADMIN_USERNAME = adminUsername
  config.ADMIN_PASSWORD = adminPassword

  colorLog('green', `✅ 管理员账户已配置: ${adminUsername}`)

  // 6. 其他配置
  console.log()
  colorLog('blue', '⚙️  6. 其他配置')
  console.log('─'.repeat(30))

  config.SHARE_DEFAULT_EXPIRES = await question('分享链接默认过期时间 (默认: 24h): ') || '24h'
  config.BCRYPT_ROUNDS = await question('密码哈希强度 (默认: 12): ') || '12'
  config.PRESIGNED_URL_EXPIRES = await question('预签名 URL 过期时间/秒 (默认: 300): ') || '300'

  // 7. 生成配置文件
  console.log()
  colorLog('blue', '📝 7. 生成配置文件')
  console.log('─'.repeat(30))

  const envPath = path.join(process.cwd(), 'backend', '.env')
  const envContent = generateEnvContent(config)

  console.log()
  colorLog('cyan', '生成的配置内容：')
  console.log('─'.repeat(50))
  console.log(envContent)
  console.log('─'.repeat(50))

  const saveConfig = await question('是否保存配置到 backend/.env 文件？(Y/n): ')
  if (saveConfig.toLowerCase() !== 'n') {
    try {
      // 备份现有文件
      if (fs.existsSync(envPath)) {
        const backupPath = `${envPath}.backup.${Date.now()}`
        fs.copyFileSync(envPath, backupPath)
        colorLog('yellow', `📋 已备份现有配置到: ${backupPath}`)
      }

      // 写入新配置
      fs.writeFileSync(envPath, envContent)
      colorLog('green', '✅ 配置已保存到 backend/.env')
    } catch (error) {
      colorLog('red', `❌ 保存配置失败: ${error.message}`)
      process.exit(1)
    }
  }

  // 7. 完成提示
  console.log()
  colorLog('green', '🎉 配置完成！')
  console.log('─'.repeat(30))
  console.log()
  colorLog('cyan', '下一步操作：')
  console.log('1. 运行 "bun run dev" 启动开发服务器')
  console.log('2. 访问 http://localhost:' + config.PORT + ' 查看应用')
  console.log('3. 如需修改配置，可重新运行此脚本或直接编辑 backend/.env')
  console.log()

  if (config.R2_PUBLIC_URL) {
    colorLog('yellow', '💡 重要提示：')
    console.log('您配置了存储桶自定义域名，请确保：')
    console.log('- 域名已在对应存储桶的设置中正确配置')
    console.log('- DNS 记录已生效')
    console.log('- 存储桶权限设置为公开访问')
    console.log('- 此配置仅对该域名对应的存储桶有效')
  } else {
    colorLog('blue', 'ℹ️  关于存储桶自定义域名：')
    console.log('如果后续需要配置自定义域名：')
    console.log('1. 在 Cloudflare 控制台进入特定存储桶设置')
    console.log('2. 在"自定义域"部分添加域名')
    console.log('3. 在 backend/.env 中添加 R2_PUBLIC_URL=https://your-bucket-domain.com')
    console.log('4. 重启应用即可生效')
    console.log('注意：每个存储桶可以有不同的自定义域名')
  }

  rl.close()
}

function generateEnvContent(config) {
  return `# Server Configuration
PORT=${config.PORT}
NODE_ENV=${config.NODE_ENV}

# JWT Configuration
# 生成时间: ${new Date().toISOString()}
JWT_SECRET=${config.JWT_SECRET}
JWT_EXPIRES_IN=${config.JWT_EXPIRES_IN}

# Cloudflare R2 Configuration (S3 兼容模式)
R2_ACCOUNT_ID=${config.R2_ACCOUNT_ID}
R2_ACCESS_KEY_ID=${config.R2_ACCESS_KEY_ID}
R2_SECRET_ACCESS_KEY=${config.R2_SECRET_ACCESS_KEY}
R2_ENDPOINT=${config.R2_ENDPOINT}
R2_REGION=${config.R2_REGION}

# R2 Public URL (可选 - 为特定存储桶配置自定义域名后填入)
# 注意：这是存储桶级别的自定义域名，不是整个 R2 服务的域名
${config.R2_PUBLIC_URL ? `R2_PUBLIC_URL=${config.R2_PUBLIC_URL}` : '# R2_PUBLIC_URL=https://bucket-custom-domain.yourdomain.com'}

# Share Service Configuration
SHARE_DEFAULT_EXPIRES=${config.SHARE_DEFAULT_EXPIRES}

# Admin User Configuration
ADMIN_USERNAME=${config.ADMIN_USERNAME}
ADMIN_PASSWORD=${config.ADMIN_PASSWORD}

# Security Configuration
BCRYPT_ROUNDS=${config.BCRYPT_ROUNDS}
PRESIGNED_URL_EXPIRES=${config.PRESIGNED_URL_EXPIRES}
`
}

// 错误处理
process.on('SIGINT', () => {
  console.log()
  colorLog('yellow', '配置已取消')
  process.exit(0)
})

// 运行主函数
main().catch(error => {
  colorLog('red', `❌ 配置过程中发生错误: ${error.message}`)
  process.exit(1)
})
