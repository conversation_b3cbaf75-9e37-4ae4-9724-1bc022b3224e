#!/bin/bash

# CF-R2 Manager Setup Script
# This script helps you set up the development environment

set -e

echo "🚀 Setting up CF-R2 Manager..."

# Check if bun is installed
if ! command -v bun &> /dev/null; then
    echo "❌ Bun is not installed. Please install Bun first:"
    echo "   curl -fsSL https://bun.sh/install | bash"
    exit 1
fi

echo "✅ Bun is installed"

# Install root dependencies
echo "📦 Installing root dependencies..."
bun install

# Install backend dependencies
echo "📦 Installing backend dependencies..."
cd backend
bun install
cd ..

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
cd frontend
bun install
cd ..

# Check if environment file exists
if [ ! -f backend/.env ]; then
    echo "📝 Environment file not found. Starting configuration wizard..."
    echo ""
    echo "Choose configuration method:"
    echo "1. Interactive configuration wizard (recommended)"
    echo "2. Create basic template"
    echo ""
    read -p "Enter your choice (1/2): " config_choice

    case $config_choice in
        1)
            echo "🚀 Starting interactive configuration wizard..."
            node scripts/configure-env.js
            ;;
        2)
            echo "📝 Creating basic environment template..."
            cp backend/.env.example backend/.env

            echo "🔐 Generating JWT secret..."
            JWT_SECRET=$(node -e "console.log(require('crypto').randomBytes(64).toString('hex'))")
            if [[ "$OSTYPE" == "darwin"* ]]; then
                sed -i '' "s/JWT_SECRET=.*/JWT_SECRET=$JWT_SECRET/" backend/.env
            else
                sed -i "s/JWT_SECRET=.*/JWT_SECRET=$JWT_SECRET/" backend/.env
            fi
            echo "✅ JWT secret generated and saved"
            echo "⚠️  Please edit backend/.env with your Cloudflare R2 credentials"
            ;;
        *)
            echo "❌ Invalid choice. Creating basic template..."
            cp backend/.env.example backend/.env
            echo "⚠️  Please edit backend/.env with your configuration"
            ;;
    esac
else
    echo "✅ Environment file already exists"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Ensure backend/.env is properly configured with your Cloudflare R2 credentials"
echo "2. Run 'bun run dev' to start the development servers"
echo "3. Open http://localhost:3000 in your browser"
echo ""
echo "Configuration tools:"
echo "- Interactive wizard: node scripts/configure-env.js"
echo "- Quick setup: ./scripts/quick-setup.sh"
echo "- Generate JWT key: node scripts/generate-jwt-secret.js"
echo ""
echo "For more information, see README.md and docs/R2_CONFIGURATION.md"
