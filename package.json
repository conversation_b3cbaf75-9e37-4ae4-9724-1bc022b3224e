{"name": "cf-r2-manager-plan", "version": "1.0.0", "description": "Cloudflare R2 File Manager - A modern web-based file management system for Cloudflare R2 storage", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && bun run dev", "dev:frontend": "cd frontend && bun run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && bun run build", "build:frontend": "cd frontend && bun run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend && bun run start", "start:frontend": "cd frontend && bun run preview", "install:all": "cd backend && bun install && cd ../frontend && bun install", "type-check": "cd frontend && bun run type-check", "setup": "./scripts/setup.sh", "configure": "node scripts/configure-env.js", "quick-setup": "./scripts/quick-setup.sh", "generate-jwt": "node scripts/generate-jwt-secret.js", "validate-config": "node scripts/validate-config.js"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["cloudflare", "r2", "file-manager", "storage", "web-app", "react", "typescript", "hono", "bun"], "author": "mgrsc <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/mgrsc/cf-r2-manager-plan.git"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}}