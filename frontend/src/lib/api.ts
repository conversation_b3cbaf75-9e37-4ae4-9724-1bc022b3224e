import axios from 'axios'
import { useAuthStore } from '../stores/authStore'
import toast from 'react-hot-toast'

const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
})

// Request interceptor to add auth token
api.interceptors.request.use((config) => {
  const token = useAuthStore.getState().token
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      useAuthStore.getState().logout()
      toast.error('登录已过期，请重新登录')
    } else if (error.response?.data?.error) {
      toast.error(error.response.data.error)
    } else if (error.message) {
      toast.error(error.message)
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authApi = {
  login: (username: string, password: string) =>
    api.post('/auth/login', { username, password }),

  verify: () => api.get('/auth/verify'),

  getInfo: () => api.get('/auth/info'),
}

// Bucket API
export const bucketApi = {
  list: () => api.get('/buckets'),
  get: (bucketName: string) => api.get(`/buckets/${bucketName}`),
  checkAccess: (bucketName: string) => api.head(`/buckets/${bucketName}`),
}

// Object API
export const objectApi = {
  list: (params: {
    bucket: string
    prefix?: string
    delimiter?: string
    maxKeys?: number
    continuationToken?: string
  }) => api.get('/objects', { params }),
  
  getMetadata: (bucket: string, key: string) =>
    api.get(`/objects/${bucket}/${encodeURIComponent(key)}`),
  
  getUploadUrl: (bucket: string, key: string, contentType?: string) =>
    api.post('/objects/upload-url', { bucket, key, contentType }),
  
  getDownloadUrl: (bucket: string, key: string) =>
    api.post('/objects/download-url', { bucket, key }),

  getPublicUrl: (bucket: string, key: string) =>
    api.post('/objects/public-url', { bucket, key }),

  delete: (bucket: string, key: string) =>
    api.delete(`/objects/${bucket}/${encodeURIComponent(key)}`),
  
  batchDelete: (bucket: string, keys: string[]) =>
    api.post('/objects/delete', { bucket, keys }),
}

// Share API
export const shareApi = {
  create: (params: {
    bucket: string
    key: string
    expiresIn?: string
    maxAccess?: number
    password?: string
  }) => api.post('/share/create', params),
  
  list: () => api.get('/share'),
  
  getInfo: (shareId: string) => api.get(`/share/${shareId}/info`),
  
  access: (shareId: string, password?: string) => {
    const params = password ? { password } : {}
    return api.get(`/share/${shareId}`, { params })
  },
  
  delete: (shareId: string) => api.delete(`/share/${shareId}`),
}

export default api
