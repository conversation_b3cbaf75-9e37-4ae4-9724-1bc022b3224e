import { useState, useEffect } from 'react'
import { XMarkIcon, CheckIcon, GlobeAltIcon } from '@heroicons/react/24/outline'
import { useDomainStore } from '../stores/domainStore'
import toast from 'react-hot-toast'

interface DomainConfigDialogProps {
  bucketName: string
  onClose: () => void
}

export default function DomainConfigDialog({ bucketName, onClose }: DomainConfigDialogProps) {
  const { getDomain, setDomain, removeDomain } = useDomainStore()
  const [domain, setDomainInput] = useState('')
  const [isValid, setIsValid] = useState(true)

  useEffect(() => {
    const existingDomain = getDomain(bucketName)
    if (existingDomain) {
      setDomainInput(existingDomain)
    }
  }, [bucketName, getDomain])

  const validateDomain = (value: string) => {
    if (!value.trim()) {
      setIsValid(true)
      return true
    }

    // 检查是否是有效的URL格式
    try {
      const url = new URL(value)
      const isValidProtocol = url.protocol === 'https:' || url.protocol === 'http:'
      const isValidDomain = url.hostname.length > 0
      const valid = isValidProtocol && isValidDomain
      setIsValid(valid)
      return valid
    } catch {
      setIsValid(false)
      return false
    }
  }

  const handleDomainChange = (value: string) => {
    setDomainInput(value)
    validateDomain(value)
  }

  const handleSave = () => {
    const trimmedDomain = domain.trim()
    
    if (!trimmedDomain) {
      // 如果域名为空，删除配置
      removeDomain(bucketName)
      toast.success('已清除自定义域名配置')
      onClose()
      return
    }

    if (!validateDomain(trimmedDomain)) {
      toast.error('请输入有效的域名URL')
      return
    }

    // 确保URL格式正确（移除末尾斜杠）
    const normalizedDomain = trimmedDomain.replace(/\/$/, '')
    setDomain(bucketName, normalizedDomain)
    toast.success('自定义域名配置已保存')
    onClose()
  }

  const handleClear = () => {
    setDomainInput('')
    setIsValid(true)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <GlobeAltIcon className="h-5 w-5 text-primary-600" />
            <h2 className="text-lg font-semibold text-gray-900">配置自定义域名</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          <div>
            <p className="text-sm text-gray-600 mb-2">存储桶</p>
            <p className="font-medium text-gray-900">{bucketName}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              自定义域名 URL
            </label>
            <input
              type="url"
              value={domain}
              onChange={(e) => handleDomainChange(e.target.value)}
              placeholder="https://files.yourdomain.com"
              className={`input ${!isValid ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
            />
            {!isValid && (
              <p className="mt-1 text-sm text-red-600">
                请输入有效的URL格式，如: https://files.yourdomain.com
              </p>
            )}
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-blue-900 mb-2">配置说明</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 需要在 Cloudflare 控制台为此存储桶配置自定义域名</li>
              <li>• 确保域名已正确解析并生效</li>
              <li>• 配置后文件链接将使用自定义域名</li>
              <li>• 留空则使用默认的预签名URL</li>
            </ul>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-yellow-900 mb-2">示例配置</h4>
            <div className="text-sm text-yellow-800 space-y-1">
              <p><strong>正确格式:</strong></p>
              <p>• https://files.yourdomain.com</p>
              <p>• https://cdn.example.com</p>
              <p>• http://localhost:9000 (开发环境)</p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <button
            onClick={handleClear}
            className="text-sm text-gray-500 hover:text-gray-700"
          >
            清空
          </button>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={onClose}
              className="btn-secondary"
            >
              取消
            </button>
            
            <button
              onClick={handleSave}
              disabled={!isValid}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <CheckIcon className="h-4 w-4 mr-2" />
              保存配置
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
