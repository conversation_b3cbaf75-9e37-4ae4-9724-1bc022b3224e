import { Fragment, useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { Listbox, Transition } from '@headlessui/react'
import { ChevronUpDownIcon, CheckIcon, GlobeAltIcon } from '@heroicons/react/20/solid'
import { useFileStore } from '../stores/fileStore'
import { useDomainStore } from '../stores/domainStore'
import { formatFileSize } from '../lib/utils'
import { cn } from '../lib/utils'
import DomainConfigDialog from './DomainConfigDialog'

interface Bucket {
  name: string
  objectCount: number
  totalSize: number
  lastModified?: Date
}

interface BucketSelectorProps {
  buckets: Bucket[]
}

export default function BucketSelector({ buckets }: BucketSelectorProps) {
  const navigate = useNavigate()
  const { bucketName } = useParams()
  const { currentBucket: currentBucketName, setCurrentBucket } = useFileStore()
  const { getDomain, getAllDomains, loadDomains } = useDomainStore()
  const [domainConfigBucket, setDomainConfigBucket] = useState<string | null>(null)

  // 加载域名配置
  useEffect(() => {
    loadDomains()
  }, [loadDomains])

  // 调试：打印域名配置状态
  useEffect(() => {
    const allDomains = getAllDomains()
    console.log('🔍 Domain Store Debug:', {
      allDomains,
      buckets: buckets.map(b => ({
        name: b.name,
        hasDomain: !!getDomain(b.name),
        domain: getDomain(b.name)
      }))
    })
  }, [buckets, getDomain, getAllDomains])

  // 优先使用store中的currentBucket，如果没有则使用URL参数
  const activeBucketName = currentBucketName || bucketName
  const currentBucket = buckets.find(b => b.name === activeBucketName)

  // 确保URL参数和store状态同步
  useEffect(() => {
    if (bucketName && bucketName !== currentBucketName) {
      setCurrentBucket(bucketName)
    }
  }, [bucketName, currentBucketName, setCurrentBucket])

  const handleBucketChange = (bucket: Bucket) => {
    setCurrentBucket(bucket.name)
    navigate(`/bucket/${bucket.name}`)
  }

  const handleDomainConfig = (e: React.MouseEvent, bucketName: string) => {
    e.stopPropagation()
    setDomainConfigBucket(bucketName)
  }

  return (
    <Listbox value={currentBucket} onChange={handleBucketChange}>
      <div className="relative">
        <Listbox.Button className="relative w-64 cursor-default rounded-lg bg-white py-2 pl-3 pr-10 text-left shadow-md focus:outline-none focus-visible:border-primary-500 focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75 focus-visible:ring-offset-2 focus-visible:ring-offset-primary-300 sm:text-sm border border-gray-300">
          <span className="block truncate">
            {currentBucket ? currentBucket.name : '选择存储桶'}
          </span>
          <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
            <ChevronUpDownIcon
              className="h-5 w-5 text-gray-400"
              aria-hidden="true"
            />
          </span>
        </Listbox.Button>
        
        <Transition
          as={Fragment}
          leave="transition ease-in duration-100"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <Listbox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
            {buckets.length === 0 ? (
              <div className="relative cursor-default select-none py-2 px-4 text-gray-700">
                暂无存储桶
              </div>
            ) : (
              buckets.map((bucket) => (
                <Listbox.Option
                  key={bucket.name}
                  className={({ active }) =>
                    cn(
                      'relative cursor-default select-none py-2 pl-10 pr-4',
                      active ? 'bg-primary-100 text-primary-900' : 'text-gray-900'
                    )
                  }
                  value={bucket}
                >
                  {({ selected }) => (
                    <>
                      <div className="flex items-center justify-between">
                        <div className="flex flex-col flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <span
                              className={cn(
                                'block truncate',
                                selected ? 'font-medium' : 'font-normal'
                              )}
                            >
                              {bucket.name}
                            </span>
                            {getDomain(bucket.name) && (
                              <GlobeAltIcon
                                className="h-3 w-3 text-green-500 flex-shrink-0"
                                title="已配置自定义域名"
                              />
                            )}
                          </div>
                          <span className="text-xs text-gray-500">
                            {bucket.objectCount} 个对象 · {formatFileSize(bucket.totalSize)}
                          </span>
                        </div>

                        <button
                          onClick={(e) => handleDomainConfig(e, bucket.name)}
                          className={cn(
                            "ml-2 p-1 rounded transition-colors",
                            getDomain(bucket.name)
                              ? "text-green-500 hover:text-green-600 hover:bg-green-50"
                              : "text-gray-400 hover:text-primary-600 hover:bg-primary-50"
                          )}
                          title={getDomain(bucket.name) ? "已配置自定义域名" : "配置自定义域名"}
                        >
                          <GlobeAltIcon className="h-4 w-4" />
                        </button>
                      </div>

                      {selected ? (
                        <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-primary-600">
                          <CheckIcon className="h-5 w-5" aria-hidden="true" />
                        </span>
                      ) : null}
                    </>
                  )}
                </Listbox.Option>
              ))
            )}
          </Listbox.Options>
        </Transition>
      </div>

      {/* Domain Config Dialog */}
      {domainConfigBucket && (
        <DomainConfigDialog
          bucketName={domainConfigBucket}
          onClose={() => setDomainConfigBucket(null)}
        />
      )}
    </Listbox>
  )
}
