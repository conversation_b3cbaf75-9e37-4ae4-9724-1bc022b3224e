import { useState, useEffect } from 'react'
import { useMutation } from '@tanstack/react-query'
import { XMarkIcon, ArrowDownTrayIcon, ShareIcon } from '@heroicons/react/24/outline'
import { objectApi } from '../lib/api'
import { useFileStore } from '../stores/fileStore'
import { isImageFile, isVideoFile, isAudioFile, downloadFile } from '../lib/utils'
import GetLinkDialog from './GetLinkDialog'
import toast from 'react-hot-toast'

interface FilePreviewProps {
  fileKey: string
  onClose: () => void
}

export default function FilePreview({ fileKey, onClose }: FilePreviewProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [showGetLinkDialog, setShowGetLinkDialog] = useState(false)
  const { currentBucket } = useFileStore()

  const fileName = fileKey.split('/').pop() || fileKey

  const getPreviewUrlMutation = useMutation({
    mutationFn: () => objectApi.getDownloadUrl(currentBucket!, fileKey),
    onSuccess: (response) => {
      setPreviewUrl(response.data.downloadUrl)
    },
  })

  useEffect(() => {
    getPreviewUrlMutation.mutate()
  }, [fileKey])

  const handleDownload = () => {
    if (previewUrl) {
      downloadFile(previewUrl, fileName)
      toast.success('开始下载文件')
    }
  }

  const handleShare = () => {
    setShowGetLinkDialog(true)
  }

  const renderPreview = () => {
    if (!previewUrl) {
      return (
        <div className="flex items-center justify-center h-96">
          <div className="loading-spinner" />
        </div>
      )
    }

    if (isImageFile(fileName)) {
      return (
        <div className="flex items-center justify-center bg-gray-100 rounded-lg">
          <img
            src={previewUrl}
            alt={fileName}
            className="max-w-full max-h-96 object-contain"
            onError={() => setPreviewUrl(null)}
          />
        </div>
      )
    }

    if (isVideoFile(fileName)) {
      return (
        <div className="bg-gray-100 rounded-lg">
          <video
            src={previewUrl}
            controls
            className="w-full max-h-96"
            onError={() => setPreviewUrl(null)}
          >
            您的浏览器不支持视频播放
          </video>
        </div>
      )
    }

    if (isAudioFile(fileName)) {
      return (
        <div className="bg-gray-100 rounded-lg p-8">
          <audio
            src={previewUrl}
            controls
            className="w-full"
            onError={() => setPreviewUrl(null)}
          >
            您的浏览器不支持音频播放
          </audio>
        </div>
      )
    }

    return (
      <div className="flex items-center justify-center h-96 bg-gray-100 rounded-lg">
        <div className="text-center">
          <p className="text-gray-500">无法预览此文件类型</p>
          <button
            onClick={handleDownload}
            className="btn-primary mt-4"
          >
            下载文件
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 truncate">
            {fileName}
          </h2>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleDownload}
              className="btn-secondary"
              title="下载"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
            </button>
            
            <button
              onClick={handleShare}
              className="btn-secondary"
              title="获取链接"
            >
              <ShareIcon className="h-4 w-4" />
            </button>
            
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {renderPreview()}
        </div>
      </div>

      {/* Get Link Dialog */}
      {showGetLinkDialog && (
        <GetLinkDialog
          fileKey={fileKey}
          onClose={() => setShowGetLinkDialog(false)}
        />
      )}
    </div>
  )
}
