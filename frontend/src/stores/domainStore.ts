import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface DomainConfig {
  [bucketName: string]: string // bucket name -> custom domain URL
}

interface DomainStore {
  domains: DomainConfig
  setDomain: (bucketName: string, domain: string) => void
  removeDomain: (bucketName: string) => void
  getDomain: (bucketName: string) => string | undefined
  getAllDomains: () => DomainConfig
}

export const useDomainStore = create<DomainStore>()(
  persist(
    (set, get) => ({
      domains: {},
      
      setDomain: (bucketName: string, domain: string) => {
        set((state) => ({
          domains: {
            ...state.domains,
            [bucketName]: domain.trim()
          }
        }))
      },
      
      removeDomain: (bucketName: string) => {
        set((state) => {
          const newDomains = { ...state.domains }
          delete newDomains[bucketName]
          return { domains: newDomains }
        })
      },
      
      getDomain: (bucketName: string) => {
        return get().domains[bucketName]
      },
      
      getAllDomains: () => {
        return get().domains
      }
    }),
    {
      name: 'r2-domain-config', // localStorage key
      version: 1,
    }
  )
)
