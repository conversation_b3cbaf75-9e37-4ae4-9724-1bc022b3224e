import { create } from 'zustand'
import { domainApi } from '../lib/api'

interface DomainConfig {
  [bucketName: string]: string // bucket name -> custom domain URL
}

interface DomainStore {
  domains: DomainConfig
  isLoading: boolean
  setDomain: (bucketName: string, domain?: string) => Promise<boolean>
  getDomain: (bucketName: string) => string | undefined
  getAllDomains: () => DomainConfig
  loadDomains: () => Promise<void>
  refreshDomain: (bucketName: string) => Promise<void>
}

export const useDomainStore = create<DomainStore>((set, get) => ({
  domains: {},
  isLoading: false,

  setDomain: async (bucketName: string, domain?: string) => {
    try {
      set({ isLoading: true })

      const response = await domainApi.set(bucketName, domain)

      if (response.data) {
        // 更新本地状态
        set((state) => {
          const newDomains = { ...state.domains }
          if (domain) {
            newDomains[bucketName] = domain.trim()
          } else {
            delete newDomains[bucketName]
          }
          return { domains: newDomains }
        })
        return true
      }
      return false
    } catch (error) {
      console.error('Error setting domain:', error)
      return false
    } finally {
      set({ isLoading: false })
    }
  },

  getDomain: (bucketName: string) => {
    return get().domains[bucketName]
  },

  getAllDomains: () => {
    return get().domains
  },

  loadDomains: async () => {
    try {
      set({ isLoading: true })
      const response = await domainApi.getAll()

      if (response.data?.domains) {
        set({ domains: response.data.domains })
      }
    } catch (error) {
      console.error('Error loading domains:', error)
    } finally {
      set({ isLoading: false })
    }
  },

  refreshDomain: async (bucketName: string) => {
    try {
      const response = await domainApi.get(bucketName)

      if (response.data) {
        set((state) => {
          const newDomains = { ...state.domains }
          if (response.data.domain) {
            newDomains[bucketName] = response.data.domain
          } else {
            delete newDomains[bucketName]
          }
          return { domains: newDomains }
        })
      }
    } catch (error) {
      console.error('Error refreshing domain:', error)
    }
  },
}))
