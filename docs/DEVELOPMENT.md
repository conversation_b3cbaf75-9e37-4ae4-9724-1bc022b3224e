# 开发文档

## 项目概述

CF-R2-Manager-Plan 是一个现代化的 Cloudflare R2 文件管理系统，采用微服务架构设计。

## 技术架构

### 后端 (Backend)
- **运行时**: Bun - 高性能 JavaScript 运行时
- **框架**: Hono - 轻量级 Web 框架
- **存储**: Cloudflare R2 (S3 兼容 API)
- **认证**: JWT + bcryptjs
- **验证**: Zod 数据验证
- **API**: RESTful API 设计

### 前端 (Frontend)
- **框架**: React 18 + TypeScript
- **构建**: Vite - 快速构建工具
- **样式**: Tailwind CSS + Headless UI
- **状态**: Zustand - 轻量级状态管理
- **数据**: TanStack Query - 服务端状态管理
- **路由**: React Router v6

## 核心功能

### 已实现功能
1. **用户认证系统**
   - 用户注册/登录
   - JWT Token 验证
   - 安全密码哈希

2. **存储桶管理**
   - 列出所有存储桶
   - 获取存储桶统计信息
   - 存储桶访问权限检查

3. **文件管理**
   - 文件列表展示
   - 文件上传（拖拽支持）
   - 文件下载
   - 文件删除（单个/批量）
   - 文件预览（图片/视频/音频）
   - 文件搜索和过滤

4. **文件分享**
   - 生成安全分享链接
   - 设置过期时间
   - 访问次数限制
   - 密码保护
   - 智能 URL 生成（自动使用自定义域名或预签名 URL）

5. **用户界面**
   - 响应式设计
   - 现代化 UI 组件
   - 面包屑导航
   - 文件类型图标
   - 加载状态指示

### 待完善功能
1. **文件夹管理**
   - 创建文件夹
   - 文件夹重命名
   - 文件夹删除

2. **文件操作增强**
   - 文件重命名
   - 文件移动/复制
   - 文件元数据编辑

3. **高级功能**
   - 文件版本管理
   - 回收站功能
   - 批量操作进度显示

4. **系统优化**
   - 数据库集成（替换内存存储）
   - 缓存机制
   - 日志系统
   - 监控和指标

## 开发环境设置

### 前置要求
- Node.js >= 18.0.0
- Bun >= 1.0.0
- Cloudflare R2 账户

### 快速开始
```bash
# 克隆项目
git clone <repository-url>
cd cf-r2-web

# 运行设置脚本
./scripts/setup.sh

# 配置环境变量
cp backend/.env.example backend/.env
# 编辑 backend/.env 填入 R2 配置

# 启动开发服务器
bun run dev
```

### 开发命令
```bash
# 开发模式
bun run dev              # 同时启动前后端
bun run dev:backend      # 仅启动后端
bun run dev:frontend     # 仅启动前端

# 构建
bun run build            # 构建前后端
bun run build:backend    # 仅构建后端
bun run build:frontend   # 仅构建前端

# 测试
cd backend && bun test   # 运行后端测试
bun run type-check       # TypeScript 类型检查
```

## API 设计

### 认证流程
1. 用户注册/登录获取 JWT Token
2. 后续请求在 Header 中携带 Token
3. 服务端验证 Token 有效性

### 文件上传流程
1. 前端请求预签名上传 URL
2. 后端生成临时上传 URL
3. 前端直接上传到 R2
4. 上传完成后刷新文件列表

### 文件分享流程
1. 创建分享记录（设置过期时间等）
2. 生成唯一分享 ID
3. 访问时验证分享有效性
4. 智能生成文件 URL：
   - 如果配置了 `R2_PUBLIC_URL`：返回自定义域名直接链接
   - 否则：生成预签名 URL（5分钟有效期）

## 部署方案

### Docker 部署
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

### 生产环境建议
1. 使用 PostgreSQL 替换内存存储
2. 配置 Redis 缓存
3. 设置 CDN 加速
4. 启用 HTTPS
5. 配置监控和日志

## 安全考虑

1. **认证安全**
   - JWT Secret 足够复杂
   - Token 过期时间合理
   - 密码强度要求

2. **文件安全**
   - 文件类型验证
   - 文件大小限制
   - 预签名 URL 短期有效

3. **API 安全**
   - CORS 配置
   - 请求频率限制
   - 输入数据验证

## 性能优化

1. **前端优化**
   - 代码分割
   - 懒加载
   - 图片优化
   - 缓存策略

2. **后端优化**
   - 数据库索引
   - 查询优化
   - 连接池
   - 缓存机制

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request

### 代码规范
- 使用 TypeScript
- 遵循 ESLint 规则
- 编写测试用例
- 更新文档
