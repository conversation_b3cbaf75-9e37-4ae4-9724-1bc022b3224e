# 配置指南

本文档提供了 CF-R2 Manager 的完整配置指南，包括多种配置方法和故障排除。

## 🚀 快速开始

### 推荐方法：交互式配置向导

```bash
# 启动交互式配置向导
npm run configure
```

向导会自动引导您完成所有必要的配置步骤。

## 📋 配置方法对比

| 方法 | 适用场景 | 优点 | 缺点 |
|------|----------|------|------|
| 交互式向导 | 首次配置、不熟悉配置 | 引导完整、自动验证 | 需要交互操作 |
| 快速设置 | 有经验用户 | 快速、灵活 | 需要手动编辑 |
| 手动配置 | 自定义需求 | 完全控制 | 容易出错 |

## 🛠️ 详细配置步骤

### 方法 1：交互式配置向导

```bash
# 完整的交互式配置
npm run configure

# 或者直接运行脚本
node scripts/configure-env.js
```

**功能特点：**
- 自动生成安全的 JWT 密钥
- 验证 Cloudflare R2 配置格式
- 引导自定义域名设置
- 实时配置验证
- 自动备份现有配置

### 方法 2：快速设置

```bash
# 快速设置脚本
npm run quick-setup

# 或者
./scripts/quick-setup.sh
```

**适用场景：**
- 有 Cloudflare R2 使用经验
- 需要快速创建基础配置
- 偏好手动编辑配置文件

### 方法 3：手动配置

```bash
# 1. 复制模板
cp backend/.env.example backend/.env

# 2. 生成 JWT 密钥
npm run generate-jwt

# 3. 手动编辑配置文件
nano backend/.env
```

## 🔧 配置项详解

### 基础服务器配置

```env
# 服务器端口
PORT=8000

# 运行环境
NODE_ENV=development
```

### JWT 安全配置

```env
# JWT 密钥 (必须至少 32 字符)
JWT_SECRET=your-generated-64-byte-hex-key

# JWT 过期时间
JWT_EXPIRES_IN=24h
```

**JWT 密钥生成方法：**
```bash
# 使用项目脚本
npm run generate-jwt

# 使用 Node.js
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"

# 使用 OpenSSL
openssl rand -hex 64
```

### Cloudflare R2 配置

```env
# 账户 ID (在 Cloudflare Dashboard 右侧边栏)
R2_ACCOUNT_ID=your-cloudflare-account-id

# API 凭据 (在 R2 > Manage R2 API tokens 中创建)
R2_ACCESS_KEY_ID=your-r2-access-key-id
R2_SECRET_ACCESS_KEY=your-r2-secret-access-key

# R2 端点 (自动生成)
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_REGION=auto
```

### 自定义域名配置

```env
# 可选：如果在 Cloudflare 控制台配置了自定义域名
R2_PUBLIC_URL=https://files.yourdomain.com
```

**配置自定义域名的好处：**
- 更简洁的文件 URL
- 无过期时间限制
- 品牌一致性
- 更好的 SEO

### 其他配置

```env
# 分享链接默认过期时间
SHARE_DEFAULT_EXPIRES=24h

# 密码哈希强度
BCRYPT_ROUNDS=12

# 预签名 URL 过期时间（秒）
PRESIGNED_URL_EXPIRES=300
```

## ✅ 配置验证

配置完成后，使用验证脚本检查配置是否正确：

```bash
# 验证配置
npm run validate-config
```

验证脚本会检查：
- 必需配置项是否完整
- JWT 密钥强度
- R2 配置格式
- 自定义域名设置
- 其他安全参数

## 🌐 自定义域名设置

### 在 Cloudflare 控制台配置

1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 进入 **R2 Object Storage**
3. 选择存储桶
4. 点击 **Settings** 标签
5. 在 **Custom Domains** 部分点击 **Connect Domain**
6. 输入子域名（如 `files.yourdomain.com`）
7. 完成 DNS 配置

### 在应用中配置

```env
# 设置自定义域名
R2_PUBLIC_URL=https://files.yourdomain.com
```

### 验证配置

```bash
# 重启应用
npm run dev

# 测试文件访问
curl https://files.yourdomain.com/bucket-name/test-file.jpg
```

## 🔍 故障排除

### 常见问题

#### 1. JWT 密钥错误
```
Error: JWT_SECRET must be at least 32 characters
```

**解决方法：**
```bash
npm run generate-jwt
# 将生成的密钥复制到 .env 文件
```

#### 2. R2 连接失败
```
Error: Invalid R2 credentials
```

**检查项目：**
- 账户 ID 是否正确
- API 凭据是否有效
- 端点 URL 格式是否正确

#### 3. 自定义域名无法访问
```
Error: 404 Not Found
```

**检查项目：**
- 域名是否在 Cloudflare 控制台正确配置
- DNS 记录是否生效
- 存储桶权限设置

#### 4. 文件上传失败
```
Error: File type not allowed
```

**解决方法：**
- 检查文件类型是否在允许列表中
- 调整 `R2_CONFIG.allowedMimeTypes` 配置

### 配置重置

如果配置出现问题，可以重置配置：

```bash
# 备份当前配置
cp backend/.env backend/.env.backup

# 重新配置
npm run configure
```

## 📚 相关文档

- [R2 详细配置文档](./R2_CONFIGURATION.md)
- [开发文档](./DEVELOPMENT.md)
- [项目 README](../README.md)

## 🆘 获取帮助

如果遇到配置问题：

1. 运行配置验证：`npm run validate-config`
2. 查看详细日志：`npm run dev`
3. 参考故障排除部分
4. 查看相关文档

## 📝 配置模板

完整的 `.env` 配置模板：

```env
# Server Configuration
PORT=8000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-generated-64-byte-hex-key
JWT_EXPIRES_IN=24h

# Cloudflare R2 Configuration
R2_ACCOUNT_ID=your-cloudflare-account-id
R2_ACCESS_KEY_ID=your-r2-access-key-id
R2_SECRET_ACCESS_KEY=your-r2-secret-access-key
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_REGION=auto

# R2 Public URL (Optional)
R2_PUBLIC_URL=https://files.yourdomain.com

# Share Service Configuration
SHARE_DEFAULT_EXPIRES=24h

# Security Configuration
BCRYPT_ROUNDS=12
PRESIGNED_URL_EXPIRES=300
```
