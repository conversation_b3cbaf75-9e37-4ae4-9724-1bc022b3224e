# Cloudflare R2 配置指南

本文档详细说明如何配置 Cloudflare R2 存储服务以及自定义域名设置。

## 📋 基础配置

### 1. 获取 R2 API 凭据

1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 进入 **R2 Object Storage** 页面
3. 点击 **Manage R2 API tokens**
4. 创建新的 API Token，记录以下信息：
   - `R2_ACCESS_KEY_ID`
   - `R2_SECRET_ACCESS_KEY`

### 2. 获取账户信息

1. 在 Cloudflare Dashboard 右侧边栏找到 **Account ID**
2. 复制账户 ID 作为 `R2_ACCOUNT_ID`

### 3. 构建 R2 端点

R2 端点格式为：`https://<ACCOUNT_ID>.r2.cloudflarestorage.com`

将 `<ACCOUNT_ID>` 替换为您的实际账户 ID。

## 🌐 自定义域名配置

### 为什么使用自定义域名？

- **更简洁的 URL**：使用您自己的域名而不是 R2 的默认域名
- **品牌一致性**：文件链接使用您的品牌域名
- **无过期限制**：直接访问，无需预签名 URL
- **更好的 SEO**：搜索引擎友好的 URL

### 配置步骤

#### 1. 在 Cloudflare 中配置自定义域名

**重要：自定义域名是针对单个存储桶配置的，不是整个 R2 服务**

1. 确保您的域名已添加到 Cloudflare
2. 进入 **R2 Object Storage** 页面
3. 选择要配置自定义域名的**特定存储桶**
4. 点击 **设置** 标签
5. 在 **自定义域** 部分点击 **连接域**
6. 输入您的子域名（如 `bucket1.yourdomain.com`）
7. 点击 **继续** 完成配置

**注意：**
- 每个存储桶可以配置不同的自定义域名
- 如果您有多个存储桶，需要为每个存储桶单独配置域名
- 一个域名只能绑定到一个存储桶

#### 2. 在应用中配置公开 URL

在 `backend/.env` 文件中设置：

```env
# 如果您为特定存储桶配置了自定义域名
R2_PUBLIC_URL=https://bucket1.yourdomain.com

# 注意：这个配置只对绑定了该域名的存储桶有效
# 其他存储桶仍会使用预签名 URL
```

**重要说明：**
- `R2_PUBLIC_URL` 应该设置为您配置的存储桶自定义域名
- 如果您有多个存储桶使用不同域名，目前只能配置一个主要域名
- 未配置自定义域名的存储桶会自动使用预签名 URL

### 域名配置示例

#### 场景 1：单个存储桶使用自定义域名
```env
# 为 "images" 存储桶配置的自定义域名
R2_PUBLIC_URL=https://images.yourdomain.com
# 文件访问：https://images.yourdomain.com/path/to/file.jpg
```

#### 场景 2：多个存储桶（推荐方案）
```env
# 只配置主要存储桶的自定义域名
R2_PUBLIC_URL=https://files.yourdomain.com
# 主存储桶文件：https://files.yourdomain.com/path/to/file.jpg
# 其他存储桶：使用预签名 URL（临时链接）
```

#### 场景 3：不使用自定义域名
```env
# 不设置 R2_PUBLIC_URL
# 所有存储桶都使用预签名 URL
```

## ⚙️ 完整配置示例

```env
# Server Configuration
PORT=8000
NODE_ENV=production

# JWT Configuration
JWT_SECRET=your-generated-64-byte-hex-key
JWT_EXPIRES_IN=24h

# Cloudflare R2 Configuration
R2_ACCOUNT_ID=abc123def456ghi789
R2_ACCESS_KEY_ID=your-access-key-id
R2_SECRET_ACCESS_KEY=your-secret-access-key
R2_ENDPOINT=https://abc123def456ghi789.r2.cloudflarestorage.com
R2_REGION=auto

# R2 Public URL (如果配置了自定义域名)
R2_PUBLIC_URL=https://files.yourdomain.com

# Share Service Configuration
SHARE_DEFAULT_EXPIRES=24h

# Security Configuration
BCRYPT_ROUNDS=12
PRESIGNED_URL_EXPIRES=300
```

## 🔧 工作原理

### 使用自定义域名时
1. 系统检测到 `R2_PUBLIC_URL` 配置
2. 直接生成公开访问 URL：`https://files.yourdomain.com/path/to/file.jpg`
3. 文件可以直接访问，无时间限制

### 不使用自定义域名时
1. 系统使用 S3 兼容 API 生成预签名 URL
2. URL 包含临时访问令牌，有时间限制（默认 5 分钟）
3. 适合私有文件或临时访问场景

## 🚨 注意事项

1. **安全性**：配置自定义域名后，文件将公开可访问
2. **缓存**：Cloudflare 会自动缓存静态文件，提高访问速度
3. **SSL**：自定义域名自动支持 HTTPS
4. **带宽**：使用自定义域名的流量计入 Cloudflare 的带宽使用

## 🔍 故障排除

### 自定义域名无法访问
1. 检查域名是否正确添加到 Cloudflare
2. 确认 DNS 记录是否正确配置
3. 验证存储桶权限设置

### 文件 404 错误
1. 检查文件路径是否正确
2. 确认存储桶名称配置
3. 验证 `R2_PUBLIC_URL` 格式

### 预签名 URL 过期
1. 检查系统时间是否正确
2. 调整 `PRESIGNED_URL_EXPIRES` 设置
3. 确认 R2 API 凭据有效性

## 📚 相关文档

- [Cloudflare R2 官方文档](https://developers.cloudflare.com/r2/)
- [R2 自定义域名配置](https://developers.cloudflare.com/r2/buckets/public-buckets/)
- [S3 兼容 API 文档](https://developers.cloudflare.com/r2/api/s3/)
